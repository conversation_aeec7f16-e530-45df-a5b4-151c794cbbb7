import pandas as pd
import numpy as np
from collections import Counter
from itertools import combinations
import argparse
import re

# 定義顏色常量
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # 結束顏色

def analyze_number_streaks():
    """
    分析每个号码未开出的期数，追踪每个号码自上次开出以来经过了多少期
    """
    # 读取CSV文件
    df = pd.read_csv('data/2015-2025.csv', encoding='utf-8')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    print('=== 号码未开出期数统计分析 ===')
    
    # 转换期号为整数
    df['period'] = df['period'].astype(int)
    df = df.sort_values('period')  # 确保按期号排序
    
    # 创建号码列表
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 初始化追踪字典，记录每个号码当前的未出现期数
    number_streaks = {}    # 记录每个号码当前的未出现期数
    
    # 初始化所有号码
    for i in range(1, 50):
        number_streaks[i] = 0
    
    # 按期分析
    streak_analysis = []

    for i, row in df.iterrows():
        current_period = row['period']

        # 获取当期开出的号码
        current_numbers = [row[col] for col in number_columns]

        # 记录本期各号码的未出现期数（在更新之前记录）
        streak_record = {
            '期号': current_period,
            '日期': row['date'],
        }

        # 添加每个号码的未出现期数（记录开奖前的状态）
        for num in range(1, 50):
            streak_record[f'号码{num}'] = number_streaks[num]

        # 更新号码状态（在记录之后更新）
        for num in range(1, 50):
            # 如果该号码在这期开出，则重置计数器
            if num in current_numbers:
                number_streaks[num] = 0
            else:
                # 否则增加未出现期数
                number_streaks[num] += 1
            
        # 统计本期信息
        streak_record['最大未出现期数'] = max(number_streaks.values())
        streak_record['平均未出现期数'] = np.mean(list(number_streaks.values()))
        streak_record['开出号码'] = current_numbers
        
        streak_analysis.append(streak_record)
    
    streak_df = pd.DataFrame(streak_analysis)
    
    # === 提前計算百分比統計數據 ===
    print('\n正在計算歷史百分比數據...')
    period_count_combinations = Counter()
    period_count_with_drawn = Counter()
    
    for _, row in streak_df.iterrows():
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period_temp = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period_temp:
                streaks_by_period_temp[streak] = []
            streaks_by_period_temp[streak].append(num)

        current_numbers = row['开出号码']
        
        for period, numbers in streaks_by_period_temp.items():
            count = len(numbers)
            combination_key = f"[{period}期][{count}次]"
            period_count_combinations[combination_key] += 1
            
            has_drawn_number = any(num in current_numbers for num in numbers)
            if has_drawn_number:
                period_count_with_drawn[combination_key] += 1
    
    # 显示最近几期的分析结果
    print('\n最近150期各号码未出现期数:')
    recent_periods = streak_df.tail(150)
    for _, row in recent_periods.iterrows():
        print(f"期号: {row['期号']} ({row['日期']})")
        # 获取每个开奖号码的未出现期数
        current_numbers = row['开出号码']
        streaks_for_numbers = [str(row[f'号码{num}']) + "期" for num in current_numbers]
        print(f"  开出号码: {row['开出号码']} " + ",".join(streaks_for_numbers))

        # 获取所有号码的未出现期数并分组
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period:
                streaks_by_period[streak] = []
            streaks_by_period[streak].append(str(num))

        # 计算新的统计
        new_stats = []
        for num in current_numbers:
            streak = row[f'号码{num}']
            count = len(streaks_by_period.get(streak, []))
            new_stats.append(str(count))
        
        print(f"--- 未連續期數數量號碼統計: {','.join(new_stats)}")

        # 统计未出现期数数量的出现次数
        period_counts = [len(v) for v in streaks_by_period.values()]
        counts_counter = Counter(period_counts)
        
        sorted_counts = sorted(counts_counter.items(), key=lambda item: item[0])
        stats_str = "  ".join([f"{count} 次: {num_groups} 組" for count, num_groups in sorted_counts])
        print(f"--- 未出現期數的數量統計 ---\n  {stats_str}")

        # 新增的統計：按"次数"对"期数"进行分组（含百分比）
        counts_to_streaks = {}
        for streak, nums in streaks_by_period.items():
            count = len(nums)
            if count not in counts_to_streaks:
                counts_to_streaks[count] = []
            counts_to_streaks[count].append(int(streak))
        
        # 格式化输出並加入百分比
        sorted_counts_keys = sorted(counts_to_streaks.keys(), reverse=True)
        
        for count in sorted_counts_keys:
            streaks = sorted(counts_to_streaks[count])
            
            # 為每個期數計算百分比並加上顏色
            period_with_percentage = []
            for period in streaks:
                combo_key = f"[{period}期][{count}次]"
                if combo_key in period_count_combinations and combo_key in period_count_with_drawn:
                    total_count = period_count_combinations[combo_key]
                    with_drawn = period_count_with_drawn[combo_key]
                    if total_count > 0:
                        percentage = (with_drawn / total_count) * 100
                        
                        # 根據百分比選擇顏色
                        if percentage >= 60:
                            color = Colors.RED + Colors.BOLD
                        elif percentage >= 50:
                            color = Colors.MAGENTA
                        elif percentage >= 40:
                            color = Colors.YELLOW
                        elif percentage >= 30:
                            color = Colors.CYAN
                        else:
                            color = Colors.GREEN
                        
                        period_with_percentage.append(f"{color}{percentage:.2f}%{Colors.END} {period}期")
                    else:
                        period_with_percentage.append(f"{Colors.GREEN}0.00%{Colors.END} {period}期")
                else:
                    period_with_percentage.append(f"{Colors.WHITE}?%{Colors.END} {period}期")
            
            periods_str = " ".join(period_with_percentage)
            print(f"   {count}次: {periods_str}")

        print(f"  最大未出现期数: {row['最大未出现期数']}")
        print("  未出现期数分組列表: ")

        # 使用辅助函数打印分组，并传入当期开出的号码
        _print_streaks_by_period(all_numbers_streaks, drawn_numbers=current_numbers)

    print("\n目前所有號碼的未出現期數分組:")
    current_streaks_list = list(number_streaks.items())
    
    # 計算百分比並顯示
    final_streaks_by_period = {}
    for num, streak in current_streaks_list:
        if streak not in final_streaks_by_period:
            final_streaks_by_period[streak] = []
        final_streaks_by_period[streak].append(num)
    
    # 顯示帶百分比的分組
    _print_streaks_by_period_with_percentage(current_streaks_list, period_count_combinations, period_count_with_drawn, is_final_summary=True)

    # Final summary stats
    final_period_counts = [len(v) for v in final_streaks_by_period.values()]
    final_counts_counter = Counter(final_period_counts)
    
    final_sorted_counts = sorted(final_counts_counter.items(), key=lambda item: item[0])
    final_stats_str = "  ".join([f"{count} 次: {num_groups} 組" for count, num_groups in final_sorted_counts])
    print(f"--- 未出現期數的數量統計 ---\n  {final_stats_str}")

    # 新增的統計：按"次数"对"期数"进行分组
    counts_to_streaks = {}
    for streak, nums in final_streaks_by_period.items():
        count = len(nums)
        if count not in counts_to_streaks:
            counts_to_streaks[count] = []
        counts_to_streaks[count].append(streak)
    
    # 格式化输出
    sorted_counts_keys = sorted(counts_to_streaks.keys(), reverse=True)
    
    for count in sorted_counts_keys:
        streaks = sorted(counts_to_streaks[count])
        
        # 為每個期數計算百分比並加上顏色
        period_with_percentage = []
        for period in streaks:
            combo_key = f"[{period}期][{count}次]"
            if combo_key in period_count_combinations and combo_key in period_count_with_drawn:
                total_count = period_count_combinations[combo_key]
                with_drawn = period_count_with_drawn[combo_key]
                if total_count > 0:
                    percentage = (with_drawn / total_count) * 100
                    
                    # 根據百分比選擇顏色
                    if percentage >= 60:
                        color = Colors.RED + Colors.BOLD
                    elif percentage >= 50:
                        color = Colors.MAGENTA
                    elif percentage >= 40:
                        color = Colors.YELLOW
                    elif percentage >= 30:
                        color = Colors.CYAN
                    else:
                        color = Colors.GREEN
                    
                    period_with_percentage.append(f"{color}{percentage:.2f}%{Colors.END} {period}期")
                else:
                    period_with_percentage.append(f"{Colors.GREEN}0.00%{Colors.END} {period}期")
            else:
                period_with_percentage.append(f"{Colors.WHITE}?%{Colors.END} {period}期")
        
        periods_str = " ".join(period_with_percentage)
        print(f"   {count}次: {periods_str}")

    # === 新增统计：特定期数和次数组合的出现频率 ===
    print('\n=== 特定期數和次數組合統計 ===')
    
    # 顯示特定組合的統計結果
    target_combinations = ["[0期][6次]", "[1期][6次]", "[2期][6次]","[3期][6次]", "[4期][6次]",
                           "[5期][6次]", "[7期][6次]", "[8期][6次]"]
    
    print("指定組合出現次數統計:")
    total_occurrences = 0
    total_with_drawn = 0
    for combo in target_combinations:
        count = period_count_combinations.get(combo, 0)
        with_drawn = period_count_with_drawn.get(combo, 0)
        total_occurrences += count
        total_with_drawn += with_drawn
        
        # 計算百分比
        if count > 0:
            percentage = (with_drawn / count) * 100
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.2f}%)")
        else:
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")
    
    print(f"\n總計指定組合出現次數: {total_occurrences}次")
    print(f"總計包含圖示♨️的次數: {total_with_drawn}次")
    
    # === 統計所有 [X期][6次] 組合 ===
    print('\n=== 所有 [X期][6次] 組合統計 ===')
    six_count_combinations = {}
    six_count_with_drawn = {}
    
    # 篩選出所有 [X期][6次] 的組合
    for combo, count in period_count_combinations.items():
        if "[6次]" in combo:
            six_count_combinations[combo] = count
            six_count_with_drawn[combo] = period_count_with_drawn.get(combo, 0)
    
    # 按期數排序（提取期數進行排序）
    def extract_period(combo_str):
        # 從 "[X期][6次]" 中提取 X
        import re
        match = re.search(r'\[(\d+)期\]', combo_str)
        return int(match.group(1)) if match else 0
    
    sorted_six_combinations = sorted(six_count_combinations.items(), key=lambda x: extract_period(x[0]))
    
    total_six_occurrences = 0
    total_six_with_drawn = 0
    
    print("所有 [X期][6次] 組合出現次數統計:")
    for combo, count in sorted_six_combinations:
        with_drawn = six_count_with_drawn[combo]
        total_six_occurrences += count
        total_six_with_drawn += with_drawn
        
        if count > 0:
            percentage = (with_drawn / count) * 100
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.2f}%)")
        else:
            print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")
    
    print(f"\n[6次] 組合總計:")
    print(f"  總共有 {len(sorted_six_combinations)} 種不同期數的 [X期][6次] 組合")
    print(f"  總出現次數: {total_six_occurrences}次")
    print(f"  總包含圖示♨️ 次數: {total_six_with_drawn}次")
    if total_six_occurrences > 0:
        overall_percentage = (total_six_with_drawn / total_six_occurrences) * 100
        print(f"  整體圖示♨️ 比例: {overall_percentage:.2f}%")

    # 顯示所有組合的前20名
    print('\n所有期數次數組合統計 (前20名):')
    most_common_combinations = period_count_combinations.most_common(20)
    for i, (combo, count) in enumerate(most_common_combinations):
        with_drawn = period_count_with_drawn.get(combo, 0)
        if count > 0:
            percentage = (with_drawn / count) * 100
            print(f"  {i+1:2d}. {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.2f}%)")
        else:
            print(f"  {i+1:2d}. {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")

    # === 新增统计：未连续期数数量号码统计的最常见组合 ===
    print('\n=== 未連續期數數量號碼統計 三個數字的組合 (前十名) ===')
    three_group_combinations_counter = Counter()
    two_group_combinations_counter = Counter()

    for _, row in streak_df.iterrows():
        all_numbers_streaks = [(num, row[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period:
                streaks_by_period[streak] = []
            streaks_by_period[streak].append(str(num))

        current_numbers = row['开出号码']
        
        new_stats = []
        for num in current_numbers:
            streak = row[f'号码{num}']
            count = len(streaks_by_period.get(streak, []))
            new_stats.append(count)
        
        new_stats.sort()
        
        # Generate all 3-number combinations from the 6 stats
        combos_3 = combinations(new_stats, 3)
        for combo in combos_3:
            three_group_combinations_counter[combo] += 1
            
        # Generate all 2-number combinations from the 6 stats
        combos_2 = combinations(new_stats, 2)
        for combo in combos_2:
            two_group_combinations_counter[combo] += 1

    # Print 3-number combos
    most_common_groups_3 = three_group_combinations_counter.most_common(10)
    for i, (group, count) in enumerate(most_common_groups_3):
        group_str = ", ".join(map(str, group))
        print(f"  {i+1:2d}. 組合: [{group_str}] - 出現次數: {count}次")

    # Print 2-number combos
    print('\n=== 未連續期數數量號碼統計 兩個數字的組合 (前十名) ===')
    most_common_groups_2 = two_group_combinations_counter.most_common(10)
    for i, (group, count) in enumerate(most_common_groups_2):
        group_str = ", ".join(map(str, group))
        print(f"  {i+1:2d}. 組合: [{group_str}] - 出現次數: {count}次")

    # 导出详细数据到CSV
    export_columns = ['期号', '日期', '开出号码', '最大未出现期数', '平均未出现期数']
    for num in range(1, 50):
        export_columns.append(f'号码{num}')
    
    export_df = streak_df[export_columns].copy()
    export_df['开出号码'] = export_df['开出号码'].apply(lambda x: ','.join(map(str, x)))
    
    export_df.to_csv('number_streaks_analysis_fixed.csv', index=False, encoding='utf-8-sig')
    print('\n详细分析结果已导出至 number_streaks_analysis_fixed.csv')
    
    return streak_df


def _print_streaks_by_period_with_percentage(streaks_list, period_count_combinations, period_count_with_drawn, drawn_numbers=None, is_final_summary=False):
    """
    帶百分比顯示的分組函數
    """
    if drawn_numbers is None:
        drawn_numbers = []

    streaks_by_period = {}
    for num, streak in streaks_list:
        if streak not in streaks_by_period:
            streaks_by_period[streak] = []
        streaks_by_period[streak].append(num)

    sorted_streaks_keys = sorted(streaks_by_period.keys(), reverse=True)
    
    lines_to_print = []
    for streak in sorted_streaks_keys:
        numbers = streaks_by_period[streak]
        numbers.sort()
        
        # 計算百分比
        count = len(numbers)
        combo_key = f"[{streak}期][{count}次]"
        total_occurrences = period_count_combinations.get(combo_key, 0)
        with_drawn = period_count_with_drawn.get(combo_key, 0)
        
        if total_occurrences > 0:
            percentage = (with_drawn / total_occurrences) * 100
            
            # 根據百分比選擇顏色
            if percentage >= 60:
                color = Colors.RED + Colors.BOLD
            elif percentage >= 50:
                color = Colors.MAGENTA
            elif percentage >= 40:
                color = Colors.YELLOW
            elif percentage >= 30:
                color = Colors.CYAN
            else:
                color = Colors.GREEN
            
            percentage_str = f"{color}{percentage:.2f}%{Colors.END}"
        else:
            percentage = 0.0
            percentage_str = f"{Colors.GREEN}0.00%{Colors.END}"

        # Format numbers with fire emoji if they were drawn
        formatted_numbers = []
        for num in numbers:
            if num in drawn_numbers and not is_final_summary:
                formatted_numbers.append(f"♨️ {num}")
            else:
                formatted_numbers.append(str(num))
        numbers_str = ", ".join(formatted_numbers)

        # 調整格式化字符串，加入百分比
        line = f"{percentage_str} [{streak:2d}期] [{count:2d}次] {numbers_str}"
        lines_to_print.append(line)

    # 雙欄打印
    midpoint = (len(lines_to_print) + 1) // 2
    left_col = lines_to_print[:midpoint]
    right_col = lines_to_print[midpoint:]

    if len(left_col) > len(right_col):
        right_col.append("")

    def get_display_width(s):
        # 移除ANSI轉義序列，以便計算真實的顯示寬度
        clean_s = re.sub(r'\033\[[0-9;]*m', '', s)
        width = 0
        for char in clean_s:
            if '\u4e00' <= char <= '\u9fff' or char in ['♨️']:
                width += 2
            else:
                width += 1
        return width

    # 找到最長的左列行（基於顯示寬度）
    max_left_width = 0
    for line in left_col:
        width = get_display_width(line)
        if width > max_left_width:
            max_left_width = width
    
    # 增加一些間距
    max_left_width += 5
    
    for left, right in zip(left_col, right_col):
        # 移除右側字符串的前導空格
        right_stripped = right.lstrip()
        # 計算左側字符串的實際顯示寬度
        current_left_width = get_display_width(left)
        # 計算需要的填充量
        padding_needed = max_left_width - current_left_width
        # 創建填充
        padding = ' ' * padding_needed
        # 打印對齊的行
        print(f"{left}{padding}{right_stripped}")
    print()

def _print_streaks_by_period(streaks_list, drawn_numbers=None, is_final_summary=False):
    """
    辅助函数，用于以双栏格式打印按期数分组的号码列表。
    如果提供了drawn_numbers，则会在对应的号码旁显示火焰符号。
    如果 is_final_summary 为 True，则调整输出格式。
    """
    if drawn_numbers is None:
        drawn_numbers = []

    streaks_by_period = {}
    for num, streak in streaks_list:
        if streak not in streaks_by_period:
            streaks_by_period[streak] = []
        streaks_by_period[streak].append(num) # Store as int for comparison

    sorted_streaks_keys = sorted(streaks_by_period.keys(), reverse=True)
    
    lines_to_print = []
    for streak in sorted_streaks_keys:
        numbers = streaks_by_period[streak]
        numbers.sort()
        
        # Format numbers with fire emoji if they were drawn
        formatted_numbers = []
        for num in numbers:
            if num in drawn_numbers and not is_final_summary:
                formatted_numbers.append(f"♨️ {num}")
            else:
                formatted_numbers.append(str(num))
        numbers_str = ", ".join(formatted_numbers)

        # 调整格式化字符串以获得更好的对齐效果
        line = f"        [{streak:2d}期] [{len(numbers):2d}次] {numbers_str}"
        lines_to_print.append(line)

    # 双栏打印
    midpoint = (len(lines_to_print) + 1) // 2
    left_col = lines_to_print[:midpoint]
    right_col = lines_to_print[midpoint:]

    # 确保两列长度一致以便zip
    if len(left_col) > len(right_col):
        right_col.append("")

    # 辅助函数来计算字符串的显示宽度
    def get_display_width(s):
        width = 0
        for char in s:
            if '\u4e00' <= char <= '\u9fff' or char in ['♨️']: # 中文字符或特殊符号
                width += 2
            else:
                width += 1
        return width

    # 计算左列的最大显示宽度
    max_left_width = 0
    for line in left_col:
        width = get_display_width(line)
        if width > max_left_width:
            max_left_width = width
    
    # 增加一些额外的填充
    max_left_width += 4
    
    for left, right in zip(left_col, right_col):
        # 移除右侧列的额外缩进
        right_stripped = right.lstrip()
        current_width = get_display_width(left)
        padding = ' ' * (max_left_width - current_width)
        print(f"{left}{padding}{right_stripped}")
    print()


def analyze_drawn_number_combinations(df, target_number, top_n=5):
    """
    分析指定号码与其他号码的组合，统计最常一起出现的配对。
    """
    print(f'\n=== 与号码 {target_number} 的组合分析 ===')
    
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 初始化配对计数器
    partner_counter = Counter()
    
    for _, row in df.iterrows():
        current_numbers = [row[col] for col in number_columns]
        
        # 检查目标号码是否在当期开出
        if target_number in current_numbers:
            # 找出所有与目标号码配对的伙伴号码
            partners = [num for num in current_numbers if num != target_number]
            for partner in partners:
                # 我们只关心伙伴号码，因为目标号码是固定的
                partner_counter[partner] += 1
                
    # 打印最常见的组合
    print(f'\n--- 与号码 {target_number} 最常一起开出的组合 (前{top_n}名) ---')
    most_common_partners = partner_counter.most_common(top_n)
    
    if not most_common_partners:
        print(f"  找不到与号码 {target_number} 的任何组合。")
        return

    for i, (partner, count) in enumerate(most_common_partners):
        # 创建有序的组合元组用于显示
        pair = tuple(sorted((target_number, partner)))
        pair_str = ", ".join(map(str, pair))
        print(f"  {i+1:2d}. 组合: ({pair_str}) - 出现次数: {count}次")



if __name__ == "__main__":
    # python "List of Unoccurred Period Groups.*******.2.py" --target-number 10 --top-n 7
    parser = argparse.ArgumentParser(description='乐透号码分析工具')
    parser.add_argument('--target-number', type=int, default=1, help='要分析其组合的目标号码 (预设: 1)')
    parser.add_argument('--top-n', type=int, default=10, help='要显示的最常见组合数量 (预设: 10)')
    args = parser.parse_args()

    streak_df = analyze_number_streaks()
    
    # 为了避免重复读取文件，我们从 streak_df 中提取所需数据
    df_for_combinations = pd.read_csv('data/2015-2025.csv', encoding='utf-8')
    df_for_combinations.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    analyze_drawn_number_combinations(df_for_combinations, target_number=args.target_number, top_n=args.top_n)