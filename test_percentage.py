#!/usr/bin/env python3
# 測試百分比格式

# 定義顏色常量
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # 結束顏色

# 測試百分比格式
test_percentages = [55.8, 58.7, 48.1, 50.1, 53.5, 42.5, 31.9, 36.5, 36.4]

print("測試百分比格式 (兩位小數點):")
for i, percentage in enumerate(test_percentages):
    # 根據百分比選擇顏色
    if percentage >= 60:
        color = Colors.RED + Colors.BOLD
    elif percentage >= 50:
        color = Colors.MAGENTA
    elif percentage >= 40:
        color = Colors.YELLOW
    elif percentage >= 30:
        color = Colors.CYAN
    else:
        color = Colors.GREEN
    
    print(f"   {i+1}次: {color}{percentage:.2f}%{Colors.END} {i}期")

print("\n測試組合統計格式:")
test_combos = [
    ("[0期][6次]", 1201, 670),
    ("[1期][6次]", 530, 311),
    ("[2期][6次]", 219, 118),
]

for combo, count, with_drawn in test_combos:
    if count > 0:
        percentage = (with_drawn / count) * 100
        print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, {percentage:.2f}%)")
    else:
        print(f"  {combo}: {count}次 (其中有圖示♨️: {with_drawn}次, 0.00%)")