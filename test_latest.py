#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from collections import Counter
import sys
import os

# Fix encoding issues on Windows
if sys.platform.startswith('win'):
    # Set console encoding to UTF-8
    os.system('chcp 65001 > nul')
    # Reconfigure stdout to handle UTF-8
    sys.stdout.reconfigure(encoding='utf-8', errors='replace')

# 定義顏色常量
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'  # 結束顏色

def test_latest_period():
    # 读取CSV文件
    df = pd.read_csv('data/2015-2025.csv', encoding='utf-8')
    df.columns = ['filename', 'game_type', 'period', 'date', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 转换期号为整数
    df['period'] = df['period'].astype(int)
    df = df.sort_values('period')
    
    # 创建号码列表
    number_columns = ['num1', 'num2', 'num3', 'num4', 'num5', 'num6']
    
    # 初始化追踪字典
    number_streaks = {}
    for i in range(1, 50):
        number_streaks[i] = 0
    
    # 按期分析
    streak_analysis = []
    period_count_combinations = Counter()
    period_count_with_drawn = Counter()

    for i, row in df.iterrows():
        current_period = row['period']
        current_numbers = [row[col] for col in number_columns]

        # 记录本期各号码的未出现期数
        streak_record = {
            '期号': current_period,
            '日期': row['date'],
        }

        # 添加每个号码的未出现期数
        for num in range(1, 50):
            streak_record[f'号码{num}'] = number_streaks[num]

        # 更新号码状态
        for num in range(1, 50):
            if num in current_numbers:
                number_streaks[num] = 0
            else:
                number_streaks[num] += 1
            
        # 统计本期信息
        streak_record['最大未出现期数'] = max(number_streaks.values())
        streak_record['平均未出现期数'] = np.mean(list(number_streaks.values()))
        streak_record['开出号码'] = current_numbers
        
        streak_analysis.append(streak_record)
        
        # 计算历史百分比数据
        all_numbers_streaks = [(num, streak_record[f'号码{num}']) for num in range(1, 50)]
        streaks_by_period = {}
        for num, streak in all_numbers_streaks:
            if streak not in streaks_by_period:
                streaks_by_period[streak] = []
            streaks_by_period[streak].append(num)

        for period, numbers in streaks_by_period.items():
            count = len(numbers)
            combination_key = f"[{period}期][{count}次]"
            period_count_combinations[combination_key] += 1
            
            has_drawn_number = any(num in current_numbers for num in numbers)
            if has_drawn_number:
                period_count_with_drawn[combination_key] += 1
    
    # 只显示最后一期
    last_record = streak_analysis[-1]
    
    print(f"期号: {last_record['期号']} ({last_record['日期']})")
    current_numbers = last_record['开出号码']
    streaks_for_numbers = [str(last_record[f'号码{num}']) + "期" for num in current_numbers]
    print(f"  开出号码: {last_record['开出号码']} " + ",".join(streaks_for_numbers))

    # 获取所有号码的未出现期数并分组
    all_numbers_streaks = [(num, last_record[f'号码{num}']) for num in range(1, 50)]
    streaks_by_period = {}
    for num, streak in all_numbers_streaks:
        if streak not in streaks_by_period:
            streaks_by_period[streak] = []
        streaks_by_period[streak].append(str(num))

    # 计算新的统计
    new_stats = []
    for num in current_numbers:
        streak = last_record[f'号码{num}']
        count = len(streaks_by_period.get(streak, []))
        new_stats.append(str(count))
    
    print(f"--- 未連續期數數量號碼統計: {','.join(new_stats)}")

    # 统计未出现期数数量的出现次数
    period_counts = [len(v) for v in streaks_by_period.values()]
    counts_counter = Counter(period_counts)
    
    sorted_counts = sorted(counts_counter.items(), key=lambda item: item[0])
    stats_str = "  ".join([f"{count} 次: {num_groups} 組" for count, num_groups in sorted_counts])
    print(f"--- 未出現期數的數量統計 ---\n  {stats_str}")

    # 新增的統計：按"次数"对"期数"进行分组
    counts_to_streaks = {}
    for streak, nums in streaks_by_period.items():
        count = len(nums)
        if count not in counts_to_streaks:
            counts_to_streaks[count] = []
        counts_to_streaks[count].append(int(streak))
    
    # 格式化输出，包含百分比
    sorted_counts_keys = sorted(counts_to_streaks.keys(), reverse=True)
    
    for count in sorted_counts_keys:
        streaks = sorted(counts_to_streaks[count])
        
        # 为每个期数计算百分比
        period_with_percentage = []
        for period in streaks:
            combo_key = f"[{period}期][{count}次]"
            if combo_key in period_count_combinations and combo_key in period_count_with_drawn:
                total_count = period_count_combinations[combo_key]
                with_drawn = period_count_with_drawn[combo_key]
                if total_count > 0:
                    percentage = (with_drawn / total_count) * 100
                    
                    # 根據百分比選擇顏色
                    if percentage >= 50:
                        color = Colors.RED + Colors.BOLD
                    elif percentage >= 45:
                        color = Colors.MAGENTA
                    elif percentage >= 40:
                        color = Colors.YELLOW
                    elif percentage >= 30:
                        color = Colors.CYAN
                    else:
                        color = Colors.GREEN
                    
                    period_with_percentage.append(f"{period}期 {color}{percentage:.1f}%{Colors.END}")
                else:
                    period_with_percentage.append(f"{period}期 {Colors.GREEN}0.0%{Colors.END}")
            else:
                period_with_percentage.append(f"{period}期 {Colors.WHITE}?%{Colors.END}")
        
        periods_str = " ".join(period_with_percentage)
        print(f"   {Colors.BOLD}{Colors.CYAN}{count}次{Colors.END}: {periods_str}")

if __name__ == "__main__":
    test_latest_period()